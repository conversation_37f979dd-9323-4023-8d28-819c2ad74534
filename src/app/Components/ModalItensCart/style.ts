import { RFValue } from "react-native-responsive-fontsize";
import { widthPercentageToDP as wp } from "react-native-responsive-screen";
import { ThemesApp } from "src/app/Context/Utils/ThemesApp";
import { IStyleProps } from "src/business/Interfaces/IStyleProps";

const customStyles = (): IStyleProps => {
  return {
    buttonGroup: {
      w: "full",
      style: {
        justifyContent: "flex-end",
      },
    },

    btnPositive: {
      style: {
        width: wp("30%"),
        backgroundColor: ThemesApp.getTheme().colors.error,
      },
    },

    btnNegative: {
      style: {
        width: wp("30%"),
      },
    },

    textModalHeader: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        textAlign: "justify",
        fontWeight: "bold",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },

    textModalBody: {
      style: {
        fontSize: RFValue(14),
        lineHeight: RFValue(16),
        textAlign: "justify",
        color: ThemesApp.getTheme().colors.textColor,
      },
    },
  };
};

export default customStyles;
