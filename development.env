ROOT_PATH = 
DATABASE_CONNECTION = postgres
DATABASE_HOST = localhost
DATABASE_USERNAME = mobile
DATABASE_PASSWORD = c3p3d1
DATABASE_NAME = DeliveryDb
DATABASE_PORT = 5432
DATABASE_URL=${DATABASE_CONNECTION}://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?schema=public


SECRET_JWT = 0f3b105187d89b7f0e622d38423fee5e96210eb61e35535bc37388078dfae67048464341cc2b6fc0cd6ce50b12cc6bb1cff42d0a81235d0c5db5d4197e240caa
ACCESS_TOKEN_DURATION = 86400000
REFRESH_TOKEN_DURATION = 864000000
REFRESH_SECRET_JWT = f89bef53875e649bb800d3a6aa03e1971c38f0e5c46bdd8b74b9a0790c62edb198448ac8fba54034155d1600a05d40854dcaf1524966e321ba56690f62263d0a
REFRESH_EXPIRATION_TIME = 10d
API_PORT=4000
CERT_DIRECTORY=server
CORS_ORIGIN = http://localhost:3000
SOCKET_PATH = 
PRISMA_FIELD_ENCRYPTION_KEY=k1.aesgcm256.xfPnpp6gACPESL5fjnLDbCBT91IjZHv8qZvos1le6Qc=

NOTIFICATION_ANDROID_CHANNEL=coop-delivery-android-channel

AWS_REGION = us-east-1
AWS_ACCESS_KEY_ID = ********************
AWS_SECRET_ACCESS_KEY = 2I0Gs0T30ccT6QT8EmFadyts1I5dnJik2zVatqFZ
AWS_BUCKET_NAME = cepedi-delivery-dev
AWS_CLOUDFRONT_DOMAIN = https://d2mgsbskfz781d.cloudfront.net
AWS_COGNITO_USER_POOL_ID = us-east-1_ZslfiKNAV
AWS_COGNITO_CLIENT_ID =3c7sj4nkdfdeofr4lklcjetvf9
AWS_COGNITO_REDIRECT_URI=coopdelivery://app
AWS_COGNITO_CLIENT_SECRET = u6cark909u0d6u5mm07qcq2due9ndsva7ppb1jh5cm9rkkeg7ie
AWS_COGNITO_CODE_EXCHANGE_URL = https://cepedi-delivery-dev.auth.us-east-1.amazoncognito.com/oauth2/token?grant_type=authorization_code

AWS_SES_EMAIL_SOURCE = Coop Delivery <<EMAIL>>
AWS_SES_ACCESS_KEY_ID = ********************
AWS_SES_SECRET_ACCESS_KEY = MLRcCWAv3bvCK3lpSV7+Ka4vNQ/oQHd6ki+7nxTc

AWS_SNS_ACCESS_KEY=********************
AWS_SNS_SECRET_ACCESS_KEY=S92zSobLtZz734xe28yyTOBLB2mrlXxOQKuxdSeh

AWS_SNS_ARN_PREFIX=arn:aws:sns:us-east-1:910682111662:
AWS_SNS_TOPIC_NOTIFICATIONS_ARN=arn:aws:sns:us-east-1:910682111662:CoopDeliveryNotifications
AWS_SNS_DELIVERYMAN_TOPIC_NOTIFICATIONS_ARN=arn:aws:sns:us-east-1:910682111662:coopdeliverymannotifications
AWS_SNS_PLATFORM_APPLICATION_ARN_FCM=arn:aws:sns:us-east-1:910682111662:app/GCM/CoopDelivery
# AWS_SNS_PLATFORM_APPLICATION_ARN_APN=

# AWS_REGION=us-east-1
# AWS_CREDENTIALS_ACCESSKEYID=********************
# AWS_CREDENTIALS_SECRETACCESSKEY=hypwV4ZiY7zsVpLPjV2f329w7nB5U0Np6IqjECBF
# AWS_BUCKET=bucketimagescepedi
# AWS_CLOUDFRONT_DOMAIN=https://d2ykjg60dypgxj.cloudfront.net
# AWS_ACCESS_KEY_ID = ********************
# AWS_SECRET_ACCESS_KEY = +fidTPQADkVZm2cvV/tzIhuOlfxMsXOZYsLH04Hg
# AWS_COGNITO_USER_POOL_ID = us-east-1_N4LYfGvKL
# AWS_COGNITO_CLIENT_ID = 1g8ujuo2d0b4pu909mi69sr5re
# AWS_COGNITO_CLIENT_SECRET = 1ukar71s1b847oug3vd2op5epim6qhjk5mjemdls2fdkrdnsi0mi
# AWS_COGNITO_REDIRECT_URI = deliverybahia://app/auth
# AWS_COGNITO_CODE_EXCHANGE_URL = https://v1.auth.us-east-1.amazoncognito.com/oauth2/token?grant_type=authorization_code


API_EXTERNAL_URL=https://webhook.site/ffc337e4-bc0f-41b9-b20c-f6c474643cd2
PAGSEGURO_MODE=SANDBOX
PAGSEGURO_LIB_URL=https://assets.pagseguro.com.br/checkout-sdk-js/rc/dist/browser/pagseguro.min.js
PAGSEGURO_ORDERS_URL=https://sandbox.api.pagseguro.com/orders
PAGSEGURO_CHARGES_URL=https://sandbox.api.pagseguro.com/charges
PAGSEGURO_SESSIONS_URL=https://sandbox.sdk.pagseguro.com/checkout-sdk/sessions
PAGSEGURO_NOTIFICATION_URL=https://ws.sandbox.pagseguro.uol.com.br/v3/transactions/notifications
PAGSEGURO_CORS_URL=https://sandbox.pagseguro.uol.com.br
PAGSEGURO_EMAIL=<EMAIL>
PAGSEGURO_TOKEN=e2de075f-9556-4230-a37c-7d5d8250abb0c52038a4449cafdaf34466f96ff561568412-145b-4ee3-a2ef-e0a2b7ad2d0d
PAGSEGURO_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr+ZqgD892U9/HXsa7XqBZUayPquAfh9xx4iwUbTSUAvTlmiXFQNTp0Bvt/5vK2FhMj39qSv1zi2OuBjvW38q1E374nzx6NNBL5JosV0+SDINTlCG0cmigHuBOyWzYmjgca+mtQu4WczCaApNaSuVqgb8u7Bd9GCOL4YJotvV5+81frlSwQXralhwRzGhj/A57CGPgGKiuPT+AOGmykIGEZsSD9RKkyoKIoc0OS8CPIzdBOtTQCIwrLn2FxI83Clcg55W8gkFSOS6rWNbG5qFZWMll6yl02HtunalHmUlRUL66YeGXdMDC2PuRcmZbGO5a/2tbVppW6mfSWG3NPRpgwIDAQAB
TZ = UTC
PAGSEGURO_FINANCIAL_URL=https://edi.api.pagseguro.com.br/edi/v1/2.01
PAGSEGURO_FINANCIAL_TOKEN=cad7c0e95bb444b48fe042d7f0c52e1d
PAGSEGURO_FINANCIAL_USERNAME=791768885

BRASIL_ABERTO_URL=https://brasilaberto.com/api
BRASIL_ABERTO_TOKEN=KECV1Az4HWGPmALIK7Ub9oMsWH1Z8jshHCaAR96yJD2O87DUBLHkVkD98sfNonxj

SERVER_CARD_DATA_PRIVATE_KEY=MIICWgIBAAKBgFBZjAJC6zyKS4awGqEOaPIqv8245teLc9ID1mG7Xt03gR2ui4V1H66kPo47tZAEwvyKXcOtXlCgUSEJxY8haxg6JQHTKtIrBpaLMT9yyz2TdrbeBtdTxJJlIuAPIdicoF9qVSAc0/cIWtwZedrRlfsXzRAe2cmIQvS1ogixTC9BAgMBAAECgYAN/1uVtUS3ujvoDdvFvPXpYish0plcaW3W41ZAllyzWw0CN7sUl8/aI8DGFH7PxRSW3B/zi8tHbnZTtBkXkzHNx2q4xhHb9hw0vRzSI5NLg2fdzrM3trJjx6DBO6XoKo9RLVtH75W3zgJH6NQyBI6N/9g71GP+nljXAlkJAAxGNQJBAJ97iAnuv7beoRlnED2VQa4T2j+FI6ActgZuVIpXJHZgPY9SzAFn+PN2oecZ3s1IchLP6asdEYMbZ7FrXO/IjTsCQQCA+g8DirrwSM+47Ko1vUIV97ipseyF2AOb+Xm6fbVTzXfr0NuFVQAg0YdUEuvJz8a3woWq1W2uZAoQTGQGGV2zAkBekxMJUNu/xiYiukuXOXvJj1xNbtbBjEUlaygXIbYtvMTEyYTREKdBOYipU14cvmlnXoRjRMmtxgKn49MpHvUhAkBv2u4Mb1NisnLb+PxA8OsQbC+NlA4Boe63ZawJbZ5FmItpS1AmmSosTCsmYeZ1tqmrkpskJkciGAaWTfntRDB7AkApPIV46Iyiz1u9tpTCANDclcmkGgNOXvulHn2bYAKz88T/CJU/4R3FA6NJBr1rCQqE0v+upP2BY/FEbmXXv8wv
APP_CARD_DATA_PUBLIC_KEY=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCK+9tYcCwj1fidGsMxBPlfBpOUd/VQnkL4DnsCtsdGG0Ee/zJFo4N9PHHrLxI0VP/cOF9Bm9YHUIQ6ScnB5H/Ssi1WyWIOZPhr11h2TCkFD7tqflb8OiSMBgfNnNrdsWYP8wjzlX6CHLp3+7aye/qkAXTayy767dNfnXrpcIyetwIDAQAB
DB_CARD_DATA_PUBLIC_KEY=MIGeMA0GCSqGSIb3DQEBAQUAA4GMADCBiAKBgEVI/X9bQPPYFjJ2769BACBQFtjghZbvyI8PHDnBicRyOo3iIliE0PztVDxEemzjryRIWWieICXPnunOpivq4MiANrDUFrDon1ufgLE5ctK60eqHxrnhw2pFldYvyMwI/5HwPjF9Aflju7OxF/IX/G7x5AxzrKs0qmgTVfBPAB6BAgMBAAE=
DB_CARD_DATA_PRIVATE_KEY=MIICWwIBAAKBgEVI/X9bQPPYFjJ2769BACBQFtjghZbvyI8PHDnBicRyOo3iIliE0PztVDxEemzjryRIWWieICXPnunOpivq4MiANrDUFrDon1ufgLE5ctK60eqHxrnhw2pFldYvyMwI/5HwPjF9Aflju7OxF/IX/G7x5AxzrKs0qmgTVfBPAB6BAgMBAAECgYAMxIeaaYmNpftpL0u6AwNv8PJU4wMbLnLKEiHEburhXZ92t/5WS5trkJyeQoxA9rA2wnJfegw4i4sgUOmEXFMxznql77DWWOIsE6bgyJvJ83I8L6h/iZZQ2mQwLbfOdryLyhcXSwSjdZim3HOFFx4zx+gpbCWuIKSB3Xc5AiQWwQJBAIl727ncEQPOQ7P5m3WENXuj2GHeE3382T5VTun4qHT42OvTbFq/BIsx8VrkoEg6vXtI5Mj7stIZAfJpIWsb+8kCQQCBAu/YpjirNnBLnbbrEkcIRmRagYrEkNJtolz3AHGdgFIR0PmzbBcd749luuLZ7NT3bGL90zK2lKcOe0Ppsnj5AkEAiDZpzSwGw5GBrHeaGYVig9wiT1x6TXHNPx/ALrmJFVNTf9qZ2RK2Cr7xlI89ajKgdtwSqDoPwm66ahTU4kz4WQJAEvyIqdzEqEXpvucmHXBiLdCWQk+gSY96uJHVlrBlCCXzbhXQOdylhk1UUEdxzVqyePtlki973i0AbPwtIX6XmQJAG3ff6fWlSxUuyDhIdCzBU22uwe+aCyNnNuQjqEEm8le9/49HsRsdxSVZw4ep7guYthb8DlOZE38AmqZ/LpL+jw==

INTEGRATION_HUB_URL=http://44.196.148.201:5880
INTEGRATION_HUB_TOKEN=https://integration-hub.auth.us-east-1.amazoncognito.com/oauth2/token
INTEGRATION_HUB_CLIENT_ID= 6vpgqv3e9tub2qllentlnju2rv
INTEGRATION_HUB_CLIENT_SECRET= 1ttt1je9bqvebkilqkfcncu44ojig5rfpv7l9sdc8v57gebuhppc




# Evolution API - Configurações
AUTHENTICATION_API_KEY=92743119f532ab28a4e57ec74dc5986b4c644e5518500b5fb94b78b284f2d6fa

CONFIG_SESSION_PHONE_VERSION=2.3000.**********

# Habilitar o uso do banco de dados
DATABASE_ENABLED=true

# Escolher o provedor do banco de dados: postgresql ou mysql
DATABASE_PROVIDER=postgresql

# URI de conexão com o banco de dados
DATABASE_CONNECTION_URI='****************************************/evolution'

# Nome do cliente para a conexão do banco de dados
DATABASE_CONNECTION_CLIENT_NAME=evolution

# Escolha os dados que você deseja salvar no banco de dados da aplicação
DATABASE_SAVE_DATA_INSTANCE=true
DATABASE_SAVE_DATA_NEW_MESSAGE=true
DATABASE_SAVE_MESSAGE_UPDATE=true
DATABASE_SAVE_DATA_CONTACTS=true
DATABASE_SAVE_DATA_CHATS=true
DATABASE_SAVE_DATA_LABELS=true
DATABASE_SAVE_DATA_HISTORIC=true


# Redis
CACHE_REDIS_ENABLED=true
CACHE_REDIS_URI=redis://redis:6379/1

# Evolution API - Configurações de CORS e Servidor
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_CREDENTIALS=true

# Configurações do servidor
SERVER_TYPE=http
SERVER_PORT=8080
SERVER_URL=http://localhost:8080

# Configurações de autenticação
AUTHENTICATION_TYPE=apikey
AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES=true

# Configurações para permitir acesso à documentação
AUTHENTICATION_GLOBAL_AUTH_TOKEN=false
AUTHENTICATION_JWT_EXPIRES_IN=3600
AUTHENTICATION_JWT_SECRET=92743119f532ab28a4e57ec74dc5986b4c644e5518500b5fb94b284f2d6fa

# Desabilitar autenticação para rotas específicas
AUTHENTICATION_IGNORE_ROUTES=/,/docs,/health,/status

# Webhook
WEBHOOK_GLOBAL_ENABLED=false
WEBHOOK_GLOBAL_URL=
WEBHOOK_GLOBAL_WEBHOOK_BY_EVENTS=false

# Configurações de instância
DEL_INSTANCE=false
DEL_TEMP_INSTANCES=true

# Log
LOG_LEVEL=ERROR
LOG_COLOR=true
LOG_BAILEYS=error

# Configurações adicionais para CORS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,apikey,x-api-key
CORS_EXPOSED_HEADERS=*

# Configurações de documentação
DOCS_ENABLED=true
DOCS_PATH=/docs