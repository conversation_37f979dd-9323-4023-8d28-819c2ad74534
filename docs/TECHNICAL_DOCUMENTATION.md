# 📚 Documentação Técnica - Backend Node.js

## 📋 Índice

1. [<PERSON>isão Geral](#-visão-geral)
2. [Arquitetura](#-arquitetura)
3. [Tecnologias Utilizadas](#-tecnologias-utilizadas)
4. [Estrutura de Pastas](#-estrutura-de-pastas)
5. [Camadas da Aplicação](#-camadas-da-aplicação)
6. [Padrões de Design](#-padrões-de-design)
7. [Banco de Dados](#-banco-de-dados)
8. [Autenticação e Autorização](#-autenticação-e-autorização)
9. [API e Documentação](#-api-e-documentação)
10. [Configuração e Deploy](#-configuração-e-deploy)
11. [Testes](#-testes)
12. [Guia de Desenvolvimento](#-guia-de-desenvolvimento)
13. [Troubleshooting](#-troubleshooting)

---

## 🎯 Visão Geral

Esta aplicação é um **backend Node.js** desenvolvido em **TypeScript** que implementa uma **arquitetura em camadas** com elementos de **Clean Architecture**. O sistema é voltado para um marketplace de delivery, oferecendo funcionalidades para usuários, lojistas, entregadores e administradores.

### Características Principais

- ✅ **Arquitetura Limpa** com separação clara de responsabilidades
- ✅ **Injeção de Dependência** usando Inversify
- ✅ **ORM Prisma** com PostgreSQL
- ✅ **Documentação Automática** com Swagger/TSOA
- ✅ **Integração AWS** (Cognito, S3, SES, SNS)
- ✅ **WebSockets** para comunicação em tempo real
- ✅ **Containerização** com Docker

---

## 🏗️ Arquitetura

### Padrão Arquitetural

A aplicação segue uma **Arquitetura em Camadas (Layered Architecture)** com princípios de **Clean Architecture**:

```
┌─────────────────┐
│   API Layer     │  ← Controllers, Middlewares, ViewModels
├─────────────────┤
│ Business Layer  │  ← Services, DTOs, Interfaces, Validators
├─────────────────┤
│Infrastructure   │  ← Repositories, Database, External APIs
└─────────────────┘
```

### Fluxo de Dados

```
Client → Controller → Service → Repository → Database
       ←            ←         ←            ←
```

**Responsabilidades por Camada:**

1. **API Layer**: Interface HTTP, autenticação, validação de entrada
2. **Business Layer**: Regras de negócio, validações, transformações
3. **Infrastructure Layer**: Acesso a dados, integrações externas

---

## 🛠️ Tecnologias Utilizadas

### Core Technologies

| Tecnologia     | Versão | Propósito           |
| -------------- | ------ | ------------------- |
| **Node.js**    | 18.x   | Runtime JavaScript  |
| **TypeScript** | 4.7.4  | Linguagem principal |
| **Express.js** | 4.17.1 | Framework web       |
| **Prisma**     | 4.16.2 | ORM e migrations    |
| **PostgreSQL** | -      | Banco de dados      |

### Frameworks e Bibliotecas

#### Injeção de Dependência

```json
{
	"inversify": "^6.0.1",
	"inversify-express-utils": "6.4.3",
	"inversify-inject-decorators": "^3.1.0"
}
```

#### Documentação e Validação

```json
{
	"tsoa": "4.1.0",
	"swagger-ui-express": "^4.4.0",
	"class-validator": "^0.13.2",
	"zod": "^3.21.4"
}
```

#### AWS Integration

```json
{
	"@aws-sdk/client-cognito-identity-provider": "^3.300.0",
	"@aws-sdk/client-s3": "^3.300.0",
	"@aws-sdk/client-ses": "^3.300.0",
	"@aws-sdk/client-sns": "^3.300.0"
}
```

#### Comunicação em Tempo Real

```json
{
	"socket.io": "^4.6.2"
}
```

#### Utilitários

```json
{
	"lodash": "^4.17.21",
	"axios": "^1.1.3",
	"winston": "^3.10.0",
	"date-fns": "^2.30.0"
}
```

---

## 📁 Estrutura de Pastas

```
📦 projeto-root/
├── 📁 src/
│   ├── 📁 api/                    # Camada de Apresentação
│   │   ├── 📁 Controllers/        # Endpoints da API
│   │   ├── 📁 Server/            # Configuração do Express
│   │   │   ├── 📁 Config/        # Configurações iniciais
│   │   │   ├── 📁 Middlewares/   # Middlewares do Express
│   │   │   └── 📁 Socket/        # WebSocket handlers
│   │   ├── 📁 Utils/             # Utilitários da API
│   │   ├── 📁 ViewModels/        # Modelos de entrada/saída
│   │   └── 📁 @types/            # Tipos TypeScript
│   │
│   ├── 📁 business/              # Camada de Negócio
│   │   ├── 📁 Services/          # Lógica de negócio
│   │   ├── 📁 DTOs/              # Data Transfer Objects
│   │   ├── 📁 Interfaces/        # Contratos e abstrações
│   │   ├── 📁 Enums/             # Enumerações
│   │   ├── 📁 Configs/           # Configurações
│   │   │   ├── 📁 Inversify/     # Container DI
│   │   │   ├── 📁 Automapper/    # Mapeamento de objetos
│   │   │   └── 📁 Swagger/       # Documentação API
│   │   ├── 📁 Middlewares/       # Middlewares de negócio
│   │   ├── 📁 Tools/             # Ferramentas auxiliares
│   │   └── 📁 Utils/             # Utilitários de negócio
│   │
│   ├── 📁 infrastructure/        # Camada de Infraestrutura
│   │   └── 📁 Data/              # Acesso a dados
│   │       └── 📁 Repositories/  # Implementação repositórios
│   │
│   └── 📁 types/                 # Tipos globais TypeScript
│
├── 📁 prisma/                    # Schema e migrations
│   ├── 📁 migrations/            # Histórico de migrations
│   ├── 📁 views/                 # Views do banco
│   └── 📄 schema.prisma          # Schema principal
│
├── 📁 Database/                  # Scripts de banco
├── 📁 n8n-backups/              # Backups de workflows
├── 📄 package.json               # Dependências
├── 📄 tsconfig.json              # Configuração TypeScript
├── 📄 docker-compose.*.yml       # Configurações Docker
└── 📄 *.env                      # Variáveis de ambiente
```

---

## 🏛️ Camadas da Aplicação

### 1. API Layer (`src/api/`)

**Responsabilidade:** Interface com o mundo externo

#### Controllers

```typescript
@controller('/user')
@Route('user')
@Tags('User')
export class UserController extends BaseController {
	constructor(
		@inject(TOKENS.IUserService) private userService: IUserService,
		@inject(TOKENS.NotificationManager)
		notificationManager: INotificationManager
	) {
		super(notificationManager);
	}

	@Security('api_key')
	@httpGet('/:id')
	@Get('{userId}')
	public async getById(
		@Request() req: express.Request,
		@Path() userId: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const user = await this.userService.getById(userId);
		const response = mapper.map(UserMapper.IUserToIUserViewModel, user);

		return this.customResponse<IUserViewModel>(response);
	}
}
```

#### Características dos Controllers

- ✅ Herdam de `BaseController`
- ✅ Usam decorators para roteamento (`@httpGet`, `@httpPost`, etc.)
- ✅ Implementam autenticação via JWT
- ✅ Retornam respostas padronizadas
- ✅ Fazem mapeamento entre DTOs e ViewModels

### 2. Business Layer (`src/business/`)

**Responsabilidade:** Regras de negócio e lógica da aplicação

#### Services

```typescript
@injectable()
export class UserService extends BaseService<IUser> implements IUserService {
	constructor(
		@inject(TOKENS.IUserRepository) repository: IUserRepository,
		@inject(TOKENS.ICognitoService) private cognitoService: ICognitoService,
		@inject(TOKENS.NotificationManager)
		notificationManager: INotificationManager
	) {
		super(repository, notificationManager);
	}

	async register(
		user: IUserRegisterDTO
	): Promise<UserAuthenticationResult | null> {
		// Validações de negócio
		if (!this.validateUserData(user)) {
			return null;
		}

		// Lógica de criação
		const cognitoUser = await this.cognitoService.createUser(user);
		const dbUser = await this.repository.create(user);

		return this.buildAuthenticationResult(cognitoUser, dbUser);
	}
}
```

#### DTOs (Data Transfer Objects)

```typescript
export interface IUserRegisterDTO {
	email: string;
	password: string;
	name: string;
	phone: string;
	cpf: string;
	birthDate: Date;
	profileIds: string[];
}
```

### 3. Infrastructure Layer (`src/infrastructure/`)

**Responsabilidade:** Acesso a dados e serviços externos

#### Repositories

```typescript
@injectable()
export class UsersRepository
	extends BaseRepository<IUser, any, any, any, any, any>
	implements IUserRepository
{
	constructor() {
		super('user');
	}

	async getByEmail(email: string): Promise<IUser | null> {
		const result = await this.databaseModel.findUnique({
			where: { email },
			include: {
				userProfiles: {
					include: { profile: true }
				}
			}
		});

		return result;
	}

	async createWithProfiles(userData: IUserRegisterDTO): Promise<IUser> {
		return await this.databaseClient.$transaction(async (tx) => {
			const user = await tx.user.create({
				data: {
					email: userData.email,
					name: userData.name
					// ... outros campos
				}
			});

			// Criar relacionamentos com perfis
			await tx.userProfile.createMany({
				data: userData.profileIds.map((profileId) => ({
					userId: user.id,
					profileId
				}))
			});

			return user;
		});
	}
}
```

---

## 🎨 Padrões de Design

### 1. Dependency Injection (Inversify)

#### Container Configuration

```typescript
// src/business/Configs/Inversify/Container.ts
const container: Container = new Container();

// Services
container.bind<IUserService>(TOKENS.IUserService).to(UserService);
container.bind<IStoreService>(TOKENS.IStoreService).to(StoreService);

// Repositories
container.bind<IUserRepository>(TOKENS.IUserRepository).to(UsersRepository);
container.bind<IStoreRepository>(TOKENS.IStoreRepository).to(StoreRepository);

// Tools
container
	.bind<INotificationManager>(TOKENS.NotificationManager)
	.to(NotificationManager)
	.inRequestScope();
container
	.bind<IUserContext>(TOKENS.UserContext)
	.to(UserContext)
	.inRequestScope();
```

#### Tokens Definition

```typescript
// src/business/Configs/Inversify/Tokens.ts
const TOKENS = {
	// Services
	IUserService: Symbol.for('IUserService'),
	IStoreService: Symbol.for('IStoreService'),

	// Repositories
	IUserRepository: Symbol.for('IUserRepository'),
	IStoreRepository: Symbol.for('IStoreRepository'),

	// Tools
	NotificationManager: Symbol.for('NotificationManager'),
	UserContext: Symbol.for('UserContext')
};
```

### 2. Repository Pattern

#### Base Repository

```typescript
@injectable()
export class BaseRepository<
	T,
	FindManyOptions,
	FindUniqueOptions,
	UpdateOptions,
	DeleteOptions,
	Include = null
> implements
		IBaseRepository<
			T,
			FindManyOptions,
			FindUniqueOptions,
			UpdateOptions,
			DeleteOptions,
			Include
		>
{
	protected databaseClient: IExtendedPrismaClient['client'];
	protected databaseModel;

	constructor(@unmanaged() type: string) {
		const databaseClient = container.get<IExtendedPrismaClient>(
			TOKENS.ExtendedPrismaClient
		).client;
		this.databaseModel = databaseClient[type];
		this.databaseClient = databaseClient;
	}

	async find(options: FindManyOptions): Promise<T[]> {
		return await this.databaseModel.findMany(options);
	}

	async findOne(options: FindUniqueOptions): Promise<T | null> {
		return await this.databaseModel.findUnique(options);
	}

	async getPaged(options: FindManyOptions): Promise<{
		result: T[];
		totalCount: number;
		totalPages: number;
	}> {
		const result: T[] = await this.databaseModel.findMany(options);
		const totalCount = await this.databaseModel.count({ where: options.where });
		const totalPages = Math.ceil(totalCount / options.take);

		return { result, totalCount, totalPages };
	}
}
```

### 3. Service Layer Pattern

#### Base Service

```typescript
@injectable()
export abstract class BaseService<T> implements IBaseService<T> {
	protected repository: IBaseRepository<T, any, any, any, any, any>;
	protected notificationManager: INotificationManager;

	constructor(
		repository: IBaseRepository<T, any, any, any, any, any>,
		notificationManager: INotificationManager
	) {
		this.repository = repository;
		this.notificationManager = notificationManager;
	}

	async getPaged(
		columnFilter: string = '',
		filterValue: string = '',
		currentPage: string = '',
		pageSize: string = '',
		orderBy: string = '',
		sortDirection: IOrder = 'asc',
		includes: any = undefined,
		where?: any,
		select?: any
	): Promise<PagedResultWithCondition<T>> {
		// Implementação de paginação com filtros
	}

	isValid(): boolean {
		const notificationList = this.notificationManager.getList();
		return notificationList.length === 0;
	}
}
```

### 4. Factory Pattern

#### Cookies Factory

```typescript
container
	.bind<interfaces.Factory<Cookies>>(TOKENS.CookiesFactory)
	.toFactory<Cookies, void[], unknown[]>(() => () => new Cookies());
```

### 5. Singleton Pattern

#### Profile Singleton

```typescript
@injectable()
export default class ProfilesSingleton {
	private profiles: IProfile[] = [];

	async loadProfiles(): Promise<void> {
		if (this.profiles.length === 0) {
			// Carregar perfis do banco
		}
	}

	getProfiles(): IProfile[] {
		return this.profiles;
	}
}
```

---

## 🗄️ Banco de Dados

### Prisma Schema

#### Configuração Principal

```prisma
generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["clientExtensions", "extendedWhereUnique", "views"]
  binaryTargets   = ["native", "darwin-arm64", "linux-musl", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

#### Exemplo de Model

```prisma
model User {
  id                   String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email                String                 @unique @db.VarChar /// @encrypted
  emailHash            String?                @db.VarChar /// @encryption:hash(email)
  name                 String                 @db.VarChar /// @encrypted
  nameHash             String?                @db.VarChar /// @encryption:hash(name)
  phone                String                 @db.VarChar /// @encrypted
  phoneHash            String?                @db.VarChar /// @encryption:hash(phone)
  cpf                  String                 @unique @db.VarChar /// @encrypted
  cpfHash              String?                @db.VarChar /// @encryption:hash(cpf)
  birthDate            DateTime               @db.Date /// @encrypted
  birthDateHash        String?                @db.VarChar /// @encryption:hash(birthDate)
  active               Boolean                @default(true)
  createdAt            DateTime               @default(now()) @db.Timestamp(6)
  updatedAt            DateTime               @default(now()) @db.Timestamp(6)

  // Relacionamentos
  userProfiles         UserProfile[]
  orders               Order[]
  reviews              Review[]
  devices              Device[]
  cards                Card[]
  userAddresses        UserAddress[]
  userFavoriteProducts UserFavoriteProducts[]
  userFavoriteStores   UserFavoriteStores[]

  // Perfis específicos
  client               Client?
  shopkeeper           Shopkeeper?
  deliveryman          Deliveryman?

  @@map("users")
}
```

### Principais Entidades

#### Core Entities

- **User** - Usuários do sistema
- **Store** - Lojas/estabelecimentos
- **Product** - Produtos
- **Order** - Pedidos
- **Address** - Endereços

#### Profile System

- **Profile** - Perfis de usuário (Cliente, Lojista, Entregador)
- **Client** - Dados específicos de clientes
- **Shopkeeper** - Dados específicos de lojistas
- **Deliveryman** - Dados específicos de entregadores

#### Business Logic

- **Category/Subcategory** - Categorização de produtos
- **OrderStatus** - Status dos pedidos
- **Transaction** - Transações financeiras
- **Review** - Avaliações e comentários

### Criptografia de Dados

A aplicação utiliza **prisma-field-encryption** para criptografar dados sensíveis:

```prisma
email     String  @db.VarChar /// @encrypted
emailHash String? @db.VarChar /// @encryption:hash(email)
```

**Campos Criptografados:**

- Dados pessoais (nome, email, telefone, CPF)
- Endereços completos
- Informações financeiras

---

## 🔐 Autenticação e Autorização

### AWS Cognito Integration

#### Configuração

```typescript
@injectable()
export class CognitoService implements ICognitoService {
	private cognitoClient: CognitoIdentityProviderClient;

	constructor() {
		this.cognitoClient = new CognitoIdentityProviderClient({
			region: process.env.AWS_REGION,
			credentials: {
				accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
				secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
			}
		});
	}

	async createUser(userData: IUserRegisterDTO): Promise<any> {
		const command = new AdminCreateUserCommand({
			UserPoolId: process.env.COGNITO_USER_POOL_ID,
			Username: userData.email,
			UserAttributes: [
				{ Name: 'email', Value: userData.email },
				{ Name: 'name', Value: userData.name }
			],
			TemporaryPassword: userData.password,
			MessageAction: 'SUPPRESS'
		});

		return await this.cognitoClient.send(command);
	}
}
```

#### Middleware de Autenticação

```typescript
@injectable()
export class AuthenticateRequest implements IAuthenticateRequest {
	constructor(
		@inject(TOKENS.ICognitoService) private cognitoService: ICognitoService,
		@inject(TOKENS.UserContext) private userContext: IUserContext
	) {}

	async handle(req: Request): Promise<boolean> {
		try {
			const token = this.extractToken(req);
			if (!token) return false;

			const payload = await this.cognitoService.verifyToken(token);
			this.userContext.setUser(payload);

			return true;
		} catch (error) {
			return false;
		}
	}

	private extractToken(req: Request): string | null {
		const authHeader = req.headers.authorization;
		return authHeader?.replace('Bearer ', '') || null;
	}
}
```

### Sistema de Perfis

#### Perfis Disponíveis

- **Cliente** - Usuários que fazem pedidos
- **Lojista** - Proprietários de estabelecimentos
- **Entregador** - Responsáveis pela entrega
- **Administrador** - Gestão do sistema

#### Controle de Acesso

```typescript
@Security("api_key")
@httpGet("/admin-only")
public async adminOnlyEndpoint(@Request() req: express.Request) {
  const authenticateResponse = await this.authenticateRequest(req);
  if (!authenticateResponse) return this.customResponse(undefined, undefined, 401);

  // Verificar se o usuário tem perfil de administrador
  const hasAdminProfile = this.userContext.hasProfile(EProfile.ADMIN);
  if (!hasAdminProfile) return this.customResponse(undefined, undefined, 403);

  // Lógica do endpoint
}
```

---

## 📖 API e Documentação

### TSOA/Swagger Integration

#### Configuração TSOA

```typescript
const specOptions: ExtendedSpecConfig = {
	name: 'API:AppDeliveryBahia',
	description: 'Swagger gerado a partir da API AppDeliveryBahia.',
	basePath: process.env.ROOT_PATH || '/',
	entryFile: './src/api/app.ts',
	specVersion: 3,
	outputDirectory: './src/business/Configs/Swagger',
	controllerPathGlobs: ['./src/api/Controllers/*.ts'],
	noImplicitAdditionalProperties: 'throw-on-extras',
	securityDefinitions: {
		api_key: {
			type: 'apiKey',
			name: 'authorization',
			in: 'header'
		}
	}
};
```

#### Decorators Utilizados

```typescript
@controller("/user")     // Define rota base
@Route("user")          // TSOA route
@Tags("User")           // Agrupamento no Swagger
@Security("api_key")    // Requer autenticação
@httpGet("/:id")        // Método HTTP
@Get("{userId}")        // TSOA path
```

### Endpoints Principais

#### Autenticação

- `POST /user/login` - Login de usuário
- `POST /user/login-web` - Login web com cookies
- `POST /user/` - Registro de usuário
- `GET /user/verify-email-code/{code}` - Verificação de email

#### Usuários

- `GET /user/{id}` - Buscar usuário por ID
- `PUT /user/{id}` - Atualizar usuário
- `GET /user/with-profile/{id}` - Usuário com perfis
- `POST /user/address` - Relacionar endereço

#### Lojas

- `GET /store` - Listar lojas
- `POST /store` - Criar loja
- `GET /store/{id}` - Buscar loja por ID
- `PUT /store/{id}` - Atualizar loja

#### Pedidos

- `GET /order` - Listar pedidos
- `POST /order` - Criar pedido
- `GET /order/{id}` - Buscar pedido por ID
- `PUT /order/{id}/status` - Atualizar status

---

## ⚙️ Configuração e Deploy

### Variáveis de Ambiente

#### Banco de Dados

```env
DATABASE_CONNECTION=postgres
DATABASE_HOST=localhost
DATABASE_USERNAME=mobile
DATABASE_PASSWORD=c3p3d1
DATABASE_NAME=DeliveryDb
DATABASE_PORT=5432
DATABASE_URL=${DATABASE_CONNECTION}://${DATABASE_USERNAME}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?schema=public
```

#### AWS Services

```env
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
COGNITO_USER_POOL_ID=your_pool_id
COGNITO_CLIENT_ID=your_client_id
S3_BUCKET_NAME=your_bucket
```

#### JWT Configuration

```env
SECRET_JWT=your_jwt_secret
ACCESS_TOKEN_DURATION=86400000
REFRESH_TOKEN_DURATION=864000000
REFRESH_SECRET_JWT=your_refresh_secret
```

### Scripts de Execução

#### Desenvolvimento

```bash
# Instalar dependências
yarn install

# Executar em modo desenvolvimento
yarn dev

# Executar com ambiente específico
yarn dev:staging
yarn dev:prod
```

#### Build e Deploy

```bash
# Build para produção
yarn build:prod

# Executar aplicação buildada
yarn start:prod

# Migrations
yarn migration:run
yarn migration:run:prod
```

#### Docker

```bash
# Build da imagem
docker build -f Dockerfile.prod -t app-backend .

# Executar com docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

---

## 🧪 Testes

### Configuração Atual

⚠️ **Status:** Jest está configurado mas **não há testes implementados**

#### Configuração Jest

```json
{
	"devDependencies": {
		"jest": "^28.1.2"
	}
}
```

### Recomendações para Implementação

#### Estrutura Sugerida

```
tests/
├── unit/
│   ├── services/
│   ├── repositories/
│   └── utils/
├── integration/
│   ├── controllers/
│   └── database/
└── e2e/
    └── api/
```

#### Exemplo de Teste Unitário

```typescript
// tests/unit/services/UserService.test.ts
describe('UserService', () => {
	let userService: UserService;
	let mockRepository: jest.Mocked<IUserRepository>;
	let mockNotificationManager: jest.Mocked<INotificationManager>;

	beforeEach(() => {
		mockRepository = createMockRepository();
		mockNotificationManager = createMockNotificationManager();
		userService = new UserService(mockRepository, mockNotificationManager);
	});

	describe('register', () => {
		it('should create user successfully', async () => {
			// Arrange
			const userData = createValidUserData();
			mockRepository.create.mockResolvedValue(userData);

			// Act
			const result = await userService.register(userData);

			// Assert
			expect(result).toBeDefined();
			expect(mockRepository.create).toHaveBeenCalledWith(userData);
		});
	});
});
```

---

## 🚀 Guia de Desenvolvimento

### Adicionando Nova Funcionalidade

#### 1. Criar Interface do Service

```typescript
// src/business/Interfaces/Service/INewFeature.ts
export interface INewFeatureService {
	create(data: INewFeatureDTO): Promise<INewFeature>;
	getById(id: string): Promise<INewFeature | null>;
	update(id: string, data: Partial<INewFeature>): Promise<boolean>;
	delete(id: string): Promise<boolean>;
}
```

#### 2. Criar DTO

```typescript
// src/business/DTOs/NewFeature/INewFeature.ts
export interface INewFeatureDTO {
	name: string;
	description: string;
	active: boolean;
}
```

#### 3. Implementar Repository

```typescript
// src/infrastructure/Data/Repositories/NewFeature.ts
@injectable()
export class NewFeatureRepository
	extends BaseRepository<INewFeature, any, any, any, any, any>
	implements INewFeatureRepository
{
	constructor() {
		super('newFeature');
	}

	async findByName(name: string): Promise<INewFeature | null> {
		return await this.databaseModel.findUnique({
			where: { name }
		});
	}
}
```

#### 4. Implementar Service

```typescript
// src/business/Services/NewFeature.ts
@injectable()
export class NewFeatureService
	extends BaseService<INewFeature>
	implements INewFeatureService
{
	constructor(
		@inject(TOKENS.INewFeatureRepository) repository: INewFeatureRepository,
		@inject(TOKENS.NotificationManager)
		notificationManager: INotificationManager
	) {
		super(repository, notificationManager);
	}

	async create(data: INewFeatureDTO): Promise<INewFeature> {
		// Validações de negócio
		if (!this.validateData(data)) {
			throw new Error('Invalid data');
		}

		return await this.repository.create(data);
	}
}
```

#### 5. Criar Controller

```typescript
// src/api/Controllers/NewFeature.ts
@controller('/new-feature')
@Route('new-feature')
@Tags('NewFeature')
export class NewFeatureController extends BaseController {
	constructor(
		@inject(TOKENS.INewFeatureService) private service: INewFeatureService,
		@inject(TOKENS.NotificationManager)
		notificationManager: INotificationManager
	) {
		super(notificationManager);
	}

	@Security('api_key')
	@httpPost('/')
	@Post('/')
	public async create(
		@Request() req: express.Request,
		@Body() data: INewFeatureViewModel
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const result = await this.service.create(data);
		return this.customResponse(result);
	}
}
```

#### 6. Registrar no Container

```typescript
// src/business/Configs/Inversify/Container.ts
container
	.bind<INewFeatureService>(TOKENS.INewFeatureService)
	.to(NewFeatureService);
container
	.bind<INewFeatureRepository>(TOKENS.INewFeatureRepository)
	.to(NewFeatureRepository);
```

#### 7. Adicionar Token

```typescript
// src/business/Configs/Inversify/Tokens.ts
const TOKENS = {
	// ... outros tokens
	INewFeatureService: Symbol.for('INewFeatureService'),
	INewFeatureRepository: Symbol.for('INewFeatureRepository')
};
```

#### 8. Importar Controller

```typescript
// src/api/app.ts
import 'src/api/Controllers/NewFeature';
```

### Boas Práticas

#### ✅ DO

- Sempre usar injeção de dependência
- Implementar interfaces antes das classes
- Validar dados nos Services
- Usar transações para operações complexas
- Documentar endpoints com decorators TSOA
- Seguir nomenclatura consistente
- Implementar tratamento de erros

#### ❌ DON'T

- Acessar banco diretamente nos Controllers
- Colocar lógica de negócio nos Repositories
- Ignorar validações de entrada
- Fazer queries N+1
- Expor dados sensíveis nas APIs
- Quebrar a separação de camadas

---

## 🔧 Troubleshooting

### Problemas Comuns

#### 1. Erro de Injeção de Dependência

```
Error: No matching bindings found for serviceIdentifier
```

**Solução:**

- Verificar se o service está registrado no Container
- Confirmar se o Token está correto
- Verificar se a interface está sendo implementada

#### 2. Erro de Conexão com Banco

```
Error: Can't reach database server
```

**Solução:**

- Verificar variáveis de ambiente
- Confirmar se o PostgreSQL está rodando
- Testar conexão com `yarn prisma:studio`

#### 3. Erro de Autenticação AWS

```
Error: The security token included in the request is invalid
```

**Solução:**

- Verificar credenciais AWS no `.env`
- Confirmar permissões IAM
- Verificar região configurada

#### 4. Erro de Migration

```
Error: Migration failed to apply cleanly
```

**Solução:**

- Verificar estado atual do banco
- Executar `yarn prisma migrate reset`
- Aplicar migrations manualmente se necessário

### Logs e Debugging

#### Winston Logger

```typescript
// Configuração de logs
const logger = winston.createLogger({
	level: 'info',
	format: winston.format.json(),
	transports: [
		new winston.transports.File({ filename: 'error.log', level: 'error' }),
		new winston.transports.Console()
	]
});
```

#### Debug Mode

```bash
# Executar com debug
DEBUG=* yarn dev

# Debug específico
DEBUG=prisma:* yarn dev
```

### Monitoramento

#### Health Checks

- Endpoint: `GET /health`
- Verifica: Banco, AWS, Redis
- Status: 200 (OK) / 503 (Service Unavailable)

#### Métricas Importantes

- Tempo de resposta das APIs
- Taxa de erro por endpoint
- Uso de memória e CPU
- Conexões ativas do banco

---

## 📚 Recursos Adicionais

### Documentação Oficial

- [Prisma Documentation](https://www.prisma.io/docs/)
- [Inversify Documentation](https://inversify.io/)
- [TSOA Documentation](https://tsoa-community.github.io/docs/)
- [Express.js Documentation](https://expressjs.com/)

### Ferramentas Úteis

- **Prisma Studio** - Interface visual do banco
- **Swagger UI** - Documentação interativa da API
- **Postman** - Testes de API
- **Docker** - Containerização

### Comandos Úteis

```bash
# Prisma
yarn prisma:generate    # Gerar cliente
yarn prisma:studio      # Interface visual
yarn prisma migrate dev # Aplicar migrations

# Build
yarn build             # Build desenvolvimento
yarn build:prod        # Build produção

# Logs
docker logs container-name -f  # Logs em tempo real
```

---

**📝 Última atualização:** Dezembro 2024
**👨‍💻 Mantenedor:** Equipe de Desenvolvimento
**📧 Contato:** <EMAIL>
