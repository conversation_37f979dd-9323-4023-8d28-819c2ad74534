{"name": "Download Audio", "type": "HTTP Request", "configuration": {"method": "GET", "url": "http://evolution_api:8080/chat/getMedia/teste", "headers": {"apikey": "92743119f532ab28a4e57ec74dc5986b4c644e5518500b5fb94b78b284f2d6fa", "Content-Type": "application/json"}, "qs": {"message": "={{ $json.messageId }}", "convertToMp4": false}, "responseFormat": "file", "outputFormat": "binary"}, "description": "Baixa o arquivo de áudio do WhatsApp via Evolution API"}