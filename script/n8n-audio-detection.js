// Função para detectar se a mensagem é um áudio
// Cole este código no nó "dados" (Function node)

const input = $input.all()[0].json;

// Verificar se é uma mensagem de áudio
const isAudio =
	input.message?.audioMessage ||
	input.message?.type === 'audioMessage' ||
	input.messageType === 'audioMessage' ||
	(input.message?.mimetype && input.message.mimetype.startsWith('audio/'));

// Extrair informações da mensagem
let messageData = {
	isAudio: isAudio,
	messageType: input.messageType || input.message?.type || 'text',
	remoteJid: input.key?.remoteJid || input.remoteJid,
	pushName: input.pushName || input.key?.pushName,
	messageId: input.key?.id || input.messageId,
	timestamp: input.messageTimestamp || Date.now()
};

if (isAudio) {
	// Dados específicos do áudio
	messageData.audioData = {
		url: input.message?.audioMessage?.url,
		mediaKey: input.message?.audioMessage?.mediaKey,
		mimetype: input.message?.audioMessage?.mimetype || 'audio/ogg',
		fileLength: input.message?.audioMessage?.fileLength,
		seconds: input.message?.audioMessage?.seconds,
		ptt: input.message?.audioMessage?.ptt || false // Push to Talk
	};
	messageData.content = '[Mensagem de áudio]';
} else {
	// Para mensagens de texto
	messageData.content =
		input.message?.conversation ||
		input.message?.extendedTextMessage?.text ||
		input.text ||
		'';
}

return [{ json: messageData }];
